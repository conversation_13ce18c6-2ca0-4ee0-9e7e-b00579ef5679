using UnityEngine;

public class DialogueManager : MonoBehaviour
{
    public GameObject dialogueInte;

    void Awake()
    {
        EventCenter.Instance.AddEvent<string>("DialogueEvents", StartDialogue);
    }

    void StartDialogue(string dialogueName)
    {
        DialogueTreeSO dialogueTree = Resources.Load<DialogueTreeSO>("Data/Dialogue/" + dialogueName);
        if (dialogueTree != null)
        {
            DialoguePlayer dialoguePlayer = dialogueInte.GetComponent<DialoguePlayer>();
            if (dialoguePlayer != null)
            {
                dialoguePlayer.dialogueTreeID = dialogueTree;
            }

            dialogueInte.SetActive(true);
        }
    }
}