using UnityEngine;

public abstract class DialogueNodeSO : ScriptableObject
{
    public string title;
    public DialogueTreeSO tree;

    [HideInInspector] public RoleSO selectedRole;
    [HideInInspector] public Sprite roleImage;
    [HideInInspector] public LeftRight leftRight; public enum LeftRight { None, Left, Right, Middle }

    [HideInInspector] public string guid;
    [HideInInspector] public Vector2 position;
}

#if UNITY_EDITOR

#endif