using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[CreateAssetMenu(menuName = "ScriptableObject/DialogueTreeSO")]
public class DialogueTreeSO : ScriptableObject
{
    public RoleList[] roleLists;
    public List<DialogueNodeSO> allNodes = new List<DialogueNodeSO>();

    [System.Serializable]
    public class RoleList
    {
        public RoleSO roles; //角色对象
        [SerializeField] public Sprite roleImage; //角色图片
        public LeftRight leftRight; public enum LeftRight { Left, Right, Middle }
        public bool exist = true; //角色是否存在，默认为真。
    }

    [HideInInspector] public Vector2 position;

#if UNITY_EDITOR

    public DialogueNodeSO CreateNode(System.Type type, Vector2 position)
    {
        DialogueNodeSO node = ScriptableObject.CreateInstance(type) as DialogueNodeSO;
        node.tree = this; // 统一赋值
        node.name = type.Name;
        node.guid = GUID.Generate().ToString();
        node.position = position;
        allNodes.Add(node);

        AssetDatabase.AddObjectToAsset(node, this);
        AssetDatabase.SaveAssets();

        return node;
    }

    public DialogueNodeSO DeleteNode(DialogueNodeSO node)
    {
        allNodes.Remove(node); // 从节点列表中移除节点
        AssetDatabase.RemoveObjectFromAsset(node); // 从资产数据库中移除节点并保存
        AssetDatabase.SaveAssets();
        return node;
    }

    public void AddChild(DialogueNodeSO parent, DialogueNodeSO child)
    {
        var childNodeField = parent.GetType().GetField("childNode");
        if (childNodeField != null)
        {
            childNodeField.SetValue(parent, child);
        }
    }

    public void RemoveChild(DialogueNodeSO parent, DialogueNodeSO child)
    {
        var childNodeField = parent.GetType().GetField("childNode");
        if (childNodeField != null)
        {
            var currentChild = childNodeField.GetValue(parent) as DialogueNodeSO;
            if (currentChild == child)
            {
                childNodeField.SetValue(parent, null);
            }
        }
    }

    public List<DialogueNodeSO> GetChildren(DialogueNodeSO parent)
    {
        var children = new List<DialogueNodeSO>();
        var childNodeField = parent.GetType().GetField("childNode");
        if (childNodeField != null)
        {
            var childNode = childNodeField.GetValue(parent) as DialogueNodeSO;
            if (childNode != null)
            {
                children.Add(childNode);
            }
        }
        return children;
    }
#endif
}