using UnityEngine;
using UnityEditor;

[CustomPropertyDrawer(typeof(DialogueTreeSO.RoleList), true)]
public class EditorDialogueTreeSO : PropertyDrawer
{
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.BeginProperty(position, label, property);

        // 获取角色对象和角色图片字段
        var rolesProperty = property.FindPropertyRelative("roles");
        var roleImageProperty = property.FindPropertyRelative("roleImage");

        // 绘制默认的属性字段
        EditorGUI.PropertyField(position, property, label, true);

        // 获取角色对象
        RoleSO roleSO = rolesProperty.objectReferenceValue as RoleSO;

        if (roleSO != null)
        {
            // 获取角色图片数组
            Sprite[] roleImages = roleSO.roleImages;

            // 获取当前选择的图片索引
            int currentIndex = System.Array.IndexOf(roleImages, roleImageProperty.objectReferenceValue);

            // 创建图片名称数组
            string[] options = new string[roleImages.Length];
            for (int i = 0; i < roleImages.Length; i++)
            {
                options[i] = roleImages[i] != null ? roleImages[i].name : "None";
            }

            // 绘制图片选择下拉框
            int selectedIndex = EditorGUI.Popup(new Rect(position.x, position.y + EditorGUIUtility.singleLineHeight * 5.5f, position.width, EditorGUIUtility.singleLineHeight), "角色形象", currentIndex, options);

            // 更新选择的图片
            if (selectedIndex >= 0 && selectedIndex < roleImages.Length)
            {
                roleImageProperty.objectReferenceValue = roleImages[selectedIndex];
            }
        }

        EditorGUI.EndProperty();
    }

    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        return EditorGUI.GetPropertyHeight(property, label, true) + EditorGUIUtility.singleLineHeight + 1.2f;
    }
}
