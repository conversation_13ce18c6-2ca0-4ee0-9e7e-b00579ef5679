using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

[CustomPropertyDrawer(typeof(NodeSequence.DialogueList))]
[CustomPropertyDrawer(typeof(NodeShout.DialogueList))]
[CustomPropertyDrawer(typeof(NodeBranching.DialogueList))]
public class EditorTextConversion : PropertyDrawer
{
    private Dictionary<string, string> textDictionary;

    public EditorTextConversion()
    {
        // 初始化字典并读取CSV文件
        textDictionary = new Dictionary<string, string>();
        string filePath = Path.Combine(Application.streamingAssetsPath, "language/简体中文.csv");
        if (File.Exists(filePath))
        {
            string[] lines = File.ReadAllLines(filePath);
            foreach (string line in lines)
            {
                string[] parts = line.Split(',');
                if (parts.Length >= 4)
                {
                    textDictionary[parts[1]] = parts[3];
                }
            }
        }
    }

    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.BeginProperty(position, label, property);

        // 使用唯一的key来存储每个元素的折叠状态
        string foldoutKey = property.propertyPath + ".foldout";
        bool foldout = EditorPrefs.GetBool(foldoutKey, true);
        foldout = EditorGUI.Foldout(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight), foldout, label);
        EditorPrefs.SetBool(foldoutKey, foldout);

        if (foldout)
        {
            // 获取所有属性
            SerializedProperty iterator = property.Copy();
            SerializedProperty endProperty = iterator.GetEndProperty();

            // 绘制所有属性
            position.y += EditorGUIUtility.singleLineHeight;
            while (iterator.NextVisible(true) && !SerializedProperty.EqualContents(iterator, endProperty))
            {
                if (iterator.name == "textID")
                {
                    // 绘制 textID 字段
                    EditorGUI.PropertyField(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight), iterator);
                    position.y += EditorGUIUtility.singleLineHeight;

                    // 根据 textID 更新 textContent
                    SerializedProperty textContentProp = property.FindPropertyRelative("textContent");
                    if (textDictionary.TryGetValue(iterator.stringValue, out string textContent))
                    {
                        textContentProp.stringValue = textContent;
                    }
                }
                else if (iterator.name == "textContent")
                {
                    // 绘制 textContent 字段（只读）
                    EditorGUI.LabelField(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight), "Text Content", iterator.stringValue);
                    position.y += EditorGUIUtility.singleLineHeight;
                }
                else
                {
                    // 绘制其他属性
                    EditorGUI.PropertyField(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight), iterator, true);
                    position.y += EditorGUI.GetPropertyHeight(iterator, true);
                }
            }
        }

        EditorGUI.EndProperty();
    }

    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        string foldoutKey = property.propertyPath + ".foldout";
        bool foldout = EditorPrefs.GetBool(foldoutKey, true);
        if (foldout)
        {
            // 计算所有属性的总高度
            float totalHeight = EditorGUIUtility.singleLineHeight;
            SerializedProperty iterator = property.Copy();
            SerializedProperty endProperty = iterator.GetEndProperty();
            while (iterator.NextVisible(true) && !SerializedProperty.EqualContents(iterator, endProperty))
            {
                totalHeight += EditorGUI.GetPropertyHeight(iterator, true);
            }
            return totalHeight;
        }
        else
        {
            return EditorGUIUtility.singleLineHeight;
        }
    }
}
