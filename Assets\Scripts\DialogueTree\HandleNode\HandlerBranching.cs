using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Linq;

public class HandlerBranching : INodeHandler
{
    private DialoguePlayer dialPlay;

    public HandlerBranching(DialoguePlayer player)
    {
        dialPlay = player;
    }

    public void HandleNode(DialogueNodeSO node)
    {
        if (dialPlay.dialogShow != null)
            dialPlay.dialogShow.text = string.Empty;

        dialPlay.dialogObj.SetActive(true);

        var branchNode = node as NodeBranching;
        if (branchNode == null) return;

        // 遍历每个分支单独处理
        foreach (var dialogue in branchNode.dialogueList)
        {
            if (dialogue.conditionPort != null)
            {
                HandleCondition(dialogue);
            }
            else
            {
                CreateBranchButton(dialogue);
            }
        }
    }

    private void CreateBranchButton(NodeBranching.DialogueList dialogue)
    {
        // 实例化按钮
        Button branchButton = GameObject.Instantiate(dialPlay.branBtn, dialPlay.branObj.transform);
        branchButton.gameObject.SetActive(true);

        // 设置按钮文本
        var buttonText = branchButton.GetComponentInChildren<TextMeshProUGUI>();
        if (buttonText != null)
        {
            buttonText.text = dialPlay.GetTextFromID(dialogue.textID);
        }

        // 设置点击事件
        branchButton.onClick.AddListener(() =>
        {
            CleanupBranchUI();
            if (dialogue.childNode != null)
                dialPlay.NodeMethods(dialogue.childNode);
        });
    }

    private void HandleCondition(NodeBranching.DialogueList dialogue)
    {

    }

    private void CleanupBranchUI()
    {
        dialPlay.branObj.SetActive(false);

        // 清理创建的按钮
        foreach (Transform child in dialPlay.branObj.transform)
        {
            if (child.gameObject != dialPlay.branBtn.gameObject)
                GameObject.Destroy(child.gameObject);
        }
    }
}