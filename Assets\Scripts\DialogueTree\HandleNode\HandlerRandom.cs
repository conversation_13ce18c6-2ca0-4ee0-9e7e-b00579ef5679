using UnityEngine;
using System.Linq;

public class HandlerRandom : INodeHandler
{
    private DialoguePlayer dialPlay;

    public HandlerRandom(DialoguePlayer player)
    {
        dialPlay = player;
    }

    public void HandleNode(DialogueNodeSO node)
    {
        var randomNode = node as NodeRandom;
        if (randomNode == null || randomNode.dialogueList == null || randomNode.dialogueList.Length == 0)
        {
            Debug.LogError("Invalid random node or empty dialogue list");
            dialPlay.EndDialogue();
            return;
        }

        // 生成0-100的随机数
        float randomValue = Random.Range(0f, 100f);
        float accumulatedPercentage = 0f;

        // 遍历所有选项，累加概率直到超过随机值
        foreach (var dialogue in randomNode.dialogueList)
        {
            if (float.TryParse(dialogue.percentage, out float percentage))
            {
                accumulatedPercentage += percentage;

                // 当累计概率超过随机值时，选中当前选项
                if (randomValue <= accumulatedPercentage)
                {
                    if (dialogue.childNode != null)
                    {
                        dialPlay.NodeMethods(dialogue.childNode);
                    }
                    else
                    {
                        Debug.LogWarning("Selected random option has no child node");
                        dialPlay.EndDialogue();
                    }
                    return;
                }
            }
        }

        // 如果由于浮点数精度问题没有选中任何选项，则选择最后一个有效选项
        var lastDialogue = randomNode.dialogueList.LastOrDefault(d => d.childNode != null);
        if (lastDialogue != null)
        {
            dialPlay.NodeMethods(lastDialogue.childNode);
        }
        else
        {
            Debug.LogError("No valid child nodes found in random node");
            dialPlay.EndDialogue();
        }
    }
}