using UnityEngine;
using System.Collections;
using DG.Tweening;
using TMPro;
using UnityEngine.UI;

public class HandlerSequence : INodeHandler
{
    private DialoguePlayer dialPlay;

    private const float CHAR_DISPLAY_DURATION = 0.02f; // 字符播放速度。
    private const float FORCE_WAIT_DURATION = 0.24f; // 强制等待时间。
    private const float SKIP_DELAY = 0.05f; // 跳过延迟时间。

    public HandlerSequence(DialoguePlayer player)
    {
        dialPlay = player;
    }

    public void HandleNode(DialogueNodeSO node)
    {
        var sequenceNode = node as NodeSequence;
        if (sequenceNode == null) return;

        HandleRolePosition(sequenceNode); // 处理角色信息
        dialPlay.dialogObj.SetActive(true); // 显示对话框
        dialPlay.StartCoroutine(DialogueCoro(sequenceNode)); // 开始对话协程
    }

    private IEnumerator DialogueCoro(NodeSequence sequenceNode) // 对话协程，处理对话序列
    {
        foreach (var dialogue in sequenceNode.dialogueList)
        {
            dialPlay.nextBtn.SetActive(false); // 开始新的对话时隐藏按钮
            string textContent = dialPlay.GetTextFromID(dialogue.textID); // 获取对话文本内容
            yield return AnimateText(textContent, dialPlay.dialogShow);
            yield return new WaitForSeconds(FORCE_WAIT_DURATION);
            dialPlay.nextBtn.SetActive(true); // 强制等待后显示按钮
            yield return WaitForKeyPress();
        }

        if (sequenceNode.childNode != null)
        {
            dialPlay.NodeMethods(sequenceNode.childNode); // 处理子节点
        }
    }

    private IEnumerator AnimateText(string text, TMPro.TextMeshProUGUI textComponent) // 逐字播放
    {
        textComponent.text = "";
        float duration = text.Length * CHAR_DISPLAY_DURATION;

        Tween tween = DOTween.To(() => 0, x =>
        {
            int visibleCount = Mathf.FloorToInt(x); // 计算当前应显示的字符数
            textComponent.text = text.Substring(0, visibleCount); // 更新文本组件显示
        }, text.Length, duration)
        .SetEase(Ease.Linear)
        .OnComplete(() => textComponent.text = text);

        while (tween.IsActive()) // 等待动画完成或用户跳过
        {
            if (Input.anyKeyDown)
            {
                tween.Complete(); // 立即完成动画
                yield return new WaitForSeconds(SKIP_DELAY); // 等待一小段时间
                break;
            }
            yield return null;
        }
    }

    private IEnumerator WaitForKeyPress()
    {
        yield return null; // 等待一帧，防止捕捉到按键
        while (!Input.anyKeyDown) { yield return null; }
        dialPlay.nextBtn.SetActive(false); // 按键后立即隐藏按钮
        yield return new WaitUntil(() => !Input.anyKey);
    }

    private void HandleRolePosition(DialogueNodeSO node)
    {
        bool isLeft = node.leftRight == DialogueNodeSO.LeftRight.Left;

        // 设置名称框显示状态
        dialPlay.leftName.SetActive(isLeft);
        dialPlay.rightName.SetActive(!isLeft);

        // 设置对话框显示状态
        (isLeft ? dialPlay.dialogLeft : dialPlay.dialogRight).SetActive(true);

        if (isLeft) // 调整渲染顺序
        {
            dialPlay.dialogLeft.transform.SetAsLastSibling();
            dialPlay.dialogRight.transform.SetAsFirstSibling();
        }
        else
        {
            dialPlay.dialogRight.transform.SetAsLastSibling();
            dialPlay.dialogLeft.transform.SetAsFirstSibling();
        }

        if (node.selectedRole != null) // 获取角色名称文本
        {
            string roleName = dialPlay.GetTextFromID(node.selectedRole.roleID);
            ((isLeft ? dialPlay.leftName : dialPlay.rightName)
                .GetComponentInChildren<TextMeshProUGUI>()).text = roleName;
        }

        if (node.roleImage != null) // 设置角色图片
        {
            ((isLeft ? dialPlay.dialogLeft : dialPlay.dialogRight)
                .GetComponent<Image>()).sprite = node.roleImage;
        }

        var activeObject = isLeft ? dialPlay.dialogLeft : dialPlay.dialogRight; // 设置动画
        var inactiveObject = isLeft ? dialPlay.dialogRight : dialPlay.dialogLeft;

        activeObject.GetComponent<RectTransform>() // 缩放动画
            .DOScale(Vector3.one, 0.16f)
            .SetEase(Ease.OutQuad);

        inactiveObject.GetComponent<RectTransform>()
            .DOScale(new Vector3(0.8f, 0.8f, 0.8f), 0.16f)
            .SetEase(Ease.OutQuad);

        activeObject.GetComponent<Image>() // 明暗动画
            .DOColor(Color.white, 0.16f)
            .SetEase(Ease.OutQuad);
        inactiveObject.GetComponent<Image>()
            .DOColor(new Color(0.5f, 0.5f, 0.5f, 1f), 0.16f)
            .SetEase(Ease.OutQuad);
    }
}
