using UnityEngine;
using System.Collections;

public class HandlerShout : INodeHandler
{
    private DialoguePlayer dialPlay;

    public HandlerShout(DialoguePlayer player)
    {
        dialPlay = player;
    }

    public void HandleNode(DialogueNodeSO node)
    {
        var shoutNode = node as NodeShout;
        if (shoutNode == null) return;

        dialPlay.shoutObj.SetActive(true);
        dialPlay.roleImage.sprite = shoutNode.roleImage ?? dialPlay.roleImage.sprite;

        dialPlay.StartCoroutine(DialCoro(shoutNode));
    }

    private IEnumerator DialCoro(NodeShout shoutNode)
    {
        foreach (var dialogue in shoutNode.dialogueList)
        {
            string textContent = dialPlay.GetTextFromID(dialogue.textID);
            dialPlay.shoutShow.text = textContent;

            yield return new WaitForSeconds(0.36f);
            yield return new WaitUntil(() => Input.anyKeyDown);
        }

        yield return new WaitUntil(() => Input.anyKeyDown);

        if (shoutNode.childNode != null)
        {
            dialPlay.NodeMethods(shoutNode.childNode);
        }
    }
}