public class NodeHandlerFactory // 节点处理器工厂
{
    private DialoguePlayer dialPlay;

    public NodeHandlerFactory(DialoguePlayer player)
    {
        dialPlay = player;
    }

    public INodeHandler CreateHandler(DialogueNodeSO node)
    {
        return node switch
        {
            NodeEnd => new HandlerEnd(dialPlay),
            NodeShout => new HandlerShout(dialPlay),
            NodeRandom => new HandlerRandom(dialPlay),
            NodeSequence => new HandlerSequence(dialPlay),
            NodeBranching => new HandlerBranching(dialPlay),
            NodeCondition => new HandlerCondition(dialPlay),
            NodeImplement => new HandlerImplement(dialPlay),
            _ => null
        };
    }
}