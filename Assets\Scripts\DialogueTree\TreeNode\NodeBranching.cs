using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.Experimental.GraphView;

public class NodeBranching : DialogueNodeSO, INodeFeature, IChildNodeSetter
{
    [System.Serializable]
    public class DialogueList
    {
        public DialogueNodeSO conditionPort;
        public bool stillShowing;
        public string textContent;
        public string textID;
        public GameObject execute;
        public DialogueNodeSO childNode;

        [HideInInspector] public string uniqueID;
    }

    public DialogueList[] dialogueList = new DialogueList[0];

    public NodeBranching()
    {
        title = "分支节点";

        dialogueList = Enumerable.Range(0, 2).Select(_ => new DialogueList
        {
            uniqueID = System.Guid.NewGuid().ToString(),
        }).ToArray(); // 添加两个默认元素
    }

    public void CreateNodeFeatures(VisualElement node)
    {
        var imgContainer = leftRight == LeftRight.Left
        ? node.Q<VisualElement>("LeftImage")
        : node.Q<VisualElement>("RightImage");

        if (imgContainer != null && roleImage != null)
        {
            imgContainer.Clear();
            var imgElement = new Image { image = roleImage.texture }; // 创建角色图片
            imgElement.AddToClassList("role-image");
            imgContainer.Add(imgElement);
        }

        var viewNode = node as ViewDialogueNode;
        if (viewNode == null)
        {
            Debug.LogError("Node is not of type ViewDialogueNode");
            return;
        }

        node.Q<VisualElement>("FunctionButtons") // 创建分支选项的按钮
        ?.Add(new Button(() => AddNodeContent(node.Q<VisualElement>("TextContent"), viewNode)) { text = "[ + ]" });

        var textContainer = node.Q<VisualElement>("TextContent");
        if (textContainer != null)
        {
            foreach (var dialogue in dialogueList)
            {
                var dialogCase = new VisualElement { name = "BranchingDialogue" }; // 创建分支选项的容器

                var inputCond = viewNode.InstantiatePort(Orientation.Horizontal, Direction.Input, Port.Capacity.Single, typeof(bool));
                inputCond.portName = "";
                inputCond.name = "BranchingPort";
                inputCond.userData = dialogue.uniqueID;
                dialogCase.Add(inputCond);

                var textField = new TextField { value = dialogue.textContent, name = "textField" };
                textField.RegisterValueChangedCallback(evt => dialogue.textID = evt.newValue);

                dialogCase.Add(textField);
                dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, dialogue.uniqueID, viewNode)) { text = "X" });

                textContainer.Add(dialogCase);

                var output = viewNode.CreateOutputPort(true); // 创建输出端口
                if (output != null)
                {
                    viewNode.outputContainer.Add(output);
                    output.userData = dialogue.uniqueID;
                }
            }
        }
    }

    private void AddNodeContent(VisualElement textContainer, ViewDialogueNode viewNode)
    {
        var newDialog = new DialogueList { uniqueID = System.Guid.NewGuid().ToString() };
        var dialogCase = new VisualElement { name = "BranchingDialogue" };

        var inputCond = viewNode.InstantiatePort(Orientation.Horizontal, Direction.Input, Port.Capacity.Single, typeof(bool));
        inputCond.portName = "";
        inputCond.name = "BranchingPort";
        inputCond.userData = newDialog.uniqueID;
        dialogCase.Add(inputCond);

        var textField = new TextField { value = "", name = "textField" };
        textField.RegisterValueChangedCallback(evt => newDialog.textID = evt.newValue);
        dialogCase.Add(textField);

        dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, newDialog.uniqueID, viewNode)) { text = "X" });
        textContainer.Add(dialogCase);

        dialogueList = dialogueList.Append(newDialog).ToArray();

        var output = viewNode.CreateOutputPort(true); // 创建输出端口
        if (output != null)
        {
            viewNode.outputContainer.Add(output);
            output.userData = newDialog.uniqueID;
        }

        viewNode.OnRefreshNode();
    }

    private void RemoveNodeContent(VisualElement container, string uniqueID, ViewDialogueNode viewNode)
    {
        var outputPort = viewNode.outputContainer.Children().FirstOrDefault(port => port.userData as string == uniqueID) as Port; // 找到对应的输出端口

        outputPort?.connections.ToList().ForEach(edge => // 删除连接线和输出端口
        {
            edge.input.Disconnect(edge);
            edge.RemoveFromHierarchy();
        });
        outputPort?.RemoveFromHierarchy();


        var inputPort = container.Children().FirstOrDefault(port => port is Port && port.userData as string == uniqueID && port.name == "BranchingPort") as Port; // 找到"BranchingPort"对应的输入端口

        inputPort.connections.ToList().ForEach(edge =>
        {
            edge.output.Disconnect(edge);
            edge.RemoveFromHierarchy();
        });
        inputPort.RemoveFromHierarchy();


        container.RemoveFromHierarchy(); // 删除容器及实例元素
        dialogueList = dialogueList.Where(d => d.uniqueID != uniqueID).ToArray();

        viewNode.OnRefreshNode();
    }

    public void SetChildNode(string uniqueID, DialogueNodeSO childNode = null) // 连线对象
    {
        var dialogueListEntry = dialogueList.FirstOrDefault(d => d.uniqueID == uniqueID);
        if (dialogueListEntry != null)
        {
            dialogueListEntry.childNode = childNode;
        }
    }

    public void SetConditionPort(string uniqueID, DialogueNodeSO conditionNode)
    {
        var dialogueListEntry = dialogueList.FirstOrDefault(d => d.uniqueID == uniqueID);
        if (dialogueListEntry != null)
        {
            dialogueListEntry.conditionPort = conditionNode;
        }
    }
}
