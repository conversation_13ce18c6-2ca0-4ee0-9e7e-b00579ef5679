using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.Experimental.GraphView;

public class NodeBranchingCond : DialogueNodeSO, INodeFeature
{
    [System.Serializable]
    public class DialogueList
    {
        public string textContent;
        public AndOr andOrs; public enum AndOr { And, Or }
        public Condition[] conditions;
        public bool candPassed = false;

        [HideInInspector] public string uniqueID;
    }

    [System.Serializable]
    public class Condition
    {
        public string textContent;
        public GameObject condType;
        public AndOr andOrs; public enum AndOr { And, Or }
        public TarObject tarObj; public enum TarObject { Player, Target }
        public RoleSO roles;
        public TarCal comparison; public enum TarCal { Greater, GreaterTo, Less, LessTo, Equal, EqualNot }
        public string candTarget;
        public string parameter;
        public bool Passed = false;
    }

    public DialogueList[] dialogueList = new DialogueList[0];

    public NodeBranchingCond()
    {
        title = "分支条件";

        dialogueList = Enumerable.Range(0, 1).Select(_ => new DialogueList
        {
            uniqueID = System.Guid.NewGuid().ToString(),
        }).ToArray(); // 添加默认元素
    }

    public void CreateNodeFeatures(VisualElement node)
    {
        if (node is not ViewDialogueNode viewNode)
        {
            Debug.LogError("Node is not of type ViewDialogueNode");
            return;
        }

        var funCont = node.Q<VisualElement>("FunctionButtons");
        var condCont = new VisualElement { name = "ConditionFunction" };

        var addButton = new Button(() => AddNodeContent(node.Q<VisualElement>("TextContent"), viewNode))
        {
            text = "[ + ]",
            name = "ConditionButton"
        };
        condCont.Add(addButton);

        var outputCond = viewNode.InstantiatePort(Orientation.Horizontal, Direction.Output, Port.Capacity.Single, typeof(bool));
        outputCond.portName = "";
        outputCond.name = "ConditionPort";
        condCont.Add(outputCond);

        funCont?.Add(condCont);

        var textContainer = node.Q<VisualElement>("TextContent");
        if (textContainer != null)
        {
            foreach (var dialogue in dialogueList)
            {
                var dialogCase = new VisualElement { name = "BranchingDialogue" }; // 创建分支选项的容器

                var textField = new TextField { value = dialogue.textContent, name = "textField" };
                textField.RegisterValueChangedCallback(evt => dialogue.textContent = evt.newValue);

                dialogCase.Add(textField);
                dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, dialogue.uniqueID, viewNode)) { text = "X" });

                textContainer.Add(dialogCase);
            }
        }
    }

    private void AddNodeContent(VisualElement textContainer, ViewDialogueNode viewNode)
    {
        var newDialog = new DialogueList { uniqueID = System.Guid.NewGuid().ToString() };
        var dialogCase = new VisualElement { name = "BranchingDialogue" };

        var textField = new TextField { value = "", name = "textField" };
        textField.RegisterValueChangedCallback(evt => newDialog.textContent = evt.newValue);
        dialogCase.Add(textField);

        dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, newDialog.uniqueID, viewNode)) { text = "X" });
        textContainer.Add(dialogCase);

        dialogueList = dialogueList.Append(newDialog).ToArray();

        viewNode.OnRefreshNode();
    }

    private void RemoveNodeContent(VisualElement container, string uniqueID, ViewDialogueNode viewNode)
    {
        container.RemoveFromHierarchy(); // 删除容器及实例元素
        dialogueList = dialogueList.Where(d => d.uniqueID != uniqueID).ToArray();

        viewNode.OnRefreshNode();
    }
}