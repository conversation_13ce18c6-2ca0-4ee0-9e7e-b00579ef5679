using UnityEngine;
using System.Linq;
using UnityEngine.UIElements;
using UnityEditor.Experimental.GraphView;

public class NodeCondition : DialogueNodeSO, INodeFeature
{
    [System.Serializable]
    public class DialogueList
    {
        public string textContent;
        public AndOr andOrs; public enum AndOr { And, Or }
        public Condition[] conditions;
        public bool candPassed = false;

        [HideInInspector] public string uniqueID;
    }

    [System.Serializable]
    public class Condition
    {
        public string textContent;
        public DialogueTreeSO condType;
        public AndOr andOrs; public enum AndOr { And, Or }
        public TarObject tarObj; public enum TarObject { Player, Target }
        public RoleSO roles;
        public TarCal comparison; public enum TarCal { Greater, GreaterTo, Less, LessTo, Equal, EqualNot }
        public string candTarget;
        public string parameter;
        public bool Passed = false;
    }

    public DialogueList[] dialogueList = new DialogueList[0];
    public DialogueNodeSO childNode;

    [Header("不满足条件的出口")]
    public DialogueNodeSO notCompliant;

    public NodeCondition()
    {
        title = "条件节点";

        dialogueList = Enumerable.Range(0, 1).Select(_ => new DialogueList
        {
            uniqueID = System.Guid.NewGuid().ToString(),
        }).ToArray(); // 添加默认元素
    }

    public void CreateNodeFeatures(VisualElement node)
    {
        if (node is not ViewDialogueNode viewNode)
        {
            Debug.LogError("Node is not of type ViewDialogueNode");
            return;
        }

        node.Q<VisualElement>("FunctionButtons") // 创建分支选项的按钮
        ?.Add(new Button(() => AddNodeContent(node.Q<VisualElement>("TextContent"), viewNode)) { text = "[ + ]" });

        var condOut = node.Q<VisualElement>("CondOutput");
        var condOutput = viewNode.InstantiatePort(Orientation.Vertical, Direction.Output, Port.Capacity.Single, typeof(DialogueNodeSO));
        condOutput.portName = "";
        condOutput.name = "CondOut";

        condOut.Add(condOutput);

        var textContainer = node.Q<VisualElement>("TextContent");
        if (textContainer != null)
        {
            foreach (var dialogue in dialogueList)
            {
                var dialogCase = new VisualElement { name = "BranchingDialogue" };

                var textField = new TextField { value = dialogue.textContent, name = "textField" };
                textField.RegisterValueChangedCallback(evt => dialogue.textContent = evt.newValue);

                dialogCase.Add(textField);
                dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, dialogue.uniqueID, viewNode)) { text = "X" });

                textContainer.Add(dialogCase);
            }
        }
    }

    private void AddNodeContent(VisualElement textContainer, ViewDialogueNode viewNode)
    {
        var newDialog = new DialogueList { uniqueID = System.Guid.NewGuid().ToString() };
        var dialogCase = new VisualElement { name = "BranchingDialogue" };

        var textField = new TextField { value = "", name = "textField" };
        textField.RegisterValueChangedCallback(evt => newDialog.textContent = evt.newValue);
        dialogCase.Add(textField);

        dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, newDialog.uniqueID, viewNode)) { text = "X" });
        textContainer.Add(dialogCase);

        dialogueList = dialogueList.Append(newDialog).ToArray();

        viewNode.OnRefreshNode();
    }

    private void RemoveNodeContent(VisualElement dialogCase, string uniqueID, ViewDialogueNode viewNode)
    {
        dialogCase.RemoveFromHierarchy(); // 删除容器及实例元素
        dialogueList = dialogueList.Where(d => d.uniqueID != uniqueID).ToArray();

        viewNode.OnRefreshNode();
    }
}

