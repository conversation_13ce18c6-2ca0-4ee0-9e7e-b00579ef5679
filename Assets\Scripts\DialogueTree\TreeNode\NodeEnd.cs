using System.Linq;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine;
using UnityEngine.UIElements;

public class NodeEnd : DialogueNodeSO, INodeFeature
{
    [System.Serializable]
    public class DialogueList
    {
        public string parameters;
        public DialogueTreeSO execute;

        [HideInInspector] public string uniqueID;
    }

    public DialogueList[] dialogueList = new DialogueList[0];

    public NodeEnd()
    {
        title = "结束节点";
    }

    public void CreateNodeFeatures(VisualElement node)
    {
        var viewNode = node as ViewDialogueNode;
        if (viewNode == null)
        {
            Debug.LogError("Node is not of type ViewDialogueNode");
            return;
        }

        var funButt = node.Q<VisualElement>("FunctionButtons");
        if (funButt != null)
        {
            funButt.Add(new Button(() => AddNodeContent(node.Q<VisualElement>("TextContent"), viewNode)) { text = "[ + ]" });

            var icon = new Image { image = EditorGUIUtility.IconContent("d_SliderJoint2D Icon").image, name = "EndIcon" };
            funButt.Add(icon);
        }

        var textContainer = node.Q<VisualElement>("TextContent");
        if (textContainer != null)
        {
            foreach (var dialogue in dialogueList)
            {
                var dialogCase = new VisualElement { name = "BranchingDialogue" };

                var objectField = new ObjectField { objectType = typeof(DialogueTreeSO), value = dialogue.execute, name = "objectField" };
                objectField.RegisterValueChangedCallback(evt => dialogue.execute = (DialogueTreeSO)evt.newValue);
                dialogCase.Add(objectField);

                var textField = new TextField { value = dialogue.parameters, name = "textField" };
                textField.RegisterValueChangedCallback(evt => dialogue.parameters = evt.newValue);

                dialogCase.Add(textField);
                dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, dialogue.uniqueID, viewNode)) { text = "X" });

                textContainer.Add(dialogCase);
            }
        }
    }

    private void AddNodeContent(VisualElement textContainer, ViewDialogueNode viewNode)
    {
        var newDialog = new DialogueList { uniqueID = System.Guid.NewGuid().ToString() };
        var dialogCase = new VisualElement { name = "BranchingDialogue" };

        var objectField = new ObjectField { objectType = typeof(DialogueTreeSO), name = "objectField" };
        objectField.RegisterValueChangedCallback(evt => newDialog.execute = (DialogueTreeSO)evt.newValue);
        dialogCase.Add(objectField);

        var textField = new TextField { value = "", name = "textField" };
        textField.RegisterValueChangedCallback(evt => newDialog.parameters = evt.newValue);
        dialogCase.Add(textField);

        dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, newDialog.uniqueID, viewNode)) { text = "X" });
        textContainer.Add(dialogCase);

        dialogueList = dialogueList.Append(newDialog).ToArray();
    }

    private void RemoveNodeContent(VisualElement container, string uniqueID, ViewDialogueNode viewNode)
    {
        container.RemoveFromHierarchy();
        dialogueList = dialogueList.Where(d => d.uniqueID != uniqueID).ToArray();
    }
}

