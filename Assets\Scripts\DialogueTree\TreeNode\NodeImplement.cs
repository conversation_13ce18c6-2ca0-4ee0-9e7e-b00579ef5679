using System.Linq;
using UnityEngine;
using UnityEditor.UIElements;
using UnityEngine.UIElements;

public class NodeImplement : DialogueNodeSO, INodeFeature
{
    [System.Serializable]
    public class DialogueList
    {
        public string parameters;
        public DialogueTreeSO execute;

        [HideInInspector] public string uniqueID;
    }

    public DialogueList[] dialogueList = new DialogueList[0];
    public DialogueNodeSO childNode;

    public NodeImplement()
    {
        title = "执行节点";

        dialogueList = Enumerable.Range(0, 1).Select(_ => new DialogueList
        {
            uniqueID = System.Guid.NewGuid().ToString(),
        }).ToArray();
    }

    public void CreateNodeFeatures(VisualElement node)
    {
        var viewNode = node as ViewDialogueNode;
        if (viewNode == null)
        {
            Debug.LogError("Node is not of type ViewDialogueNode");
            return;
        }

        node.Q<VisualElement>("FunctionButtons") // 新建元素的按钮
        ?.Add(new Button(() => AddNodeContent(node.Q<VisualElement>("TextContent"), viewNode)) { text = "[ + ]" });

        var textContainer = node.Q<VisualElement>("TextContent");
        if (textContainer != null)
        {
            foreach (var dialogue in dialogueList)
            {
                var dialogCase = new VisualElement { name = "BranchingDialogue" }; // 创建元素容器

                var objectField = new ObjectField { objectType = typeof(DialogueTreeSO), value = dialogue.execute, name = "objectField" };
                objectField.RegisterValueChangedCallback(evt => dialogue.execute = (DialogueTreeSO)evt.newValue);
                dialogCase.Add(objectField);

                var textField = new TextField { value = dialogue.parameters, name = "textField" };
                textField.RegisterValueChangedCallback(evt => dialogue.parameters = evt.newValue);

                dialogCase.Add(textField);
                dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, dialogue.uniqueID, viewNode)) { text = "X" });

                textContainer.Add(dialogCase);
            }
        }
    }

    private void AddNodeContent(VisualElement textContainer, ViewDialogueNode viewNode)
    {
        var newDialog = new DialogueList { uniqueID = System.Guid.NewGuid().ToString() };
        var dialogCase = new VisualElement { name = "BranchingDialogue" };

        var objectField = new ObjectField { objectType = typeof(DialogueTreeSO), name = "objectField" };
        objectField.RegisterValueChangedCallback(evt => newDialog.execute = (DialogueTreeSO)evt.newValue);
        dialogCase.Add(objectField);

        var textField = new TextField { value = "", name = "textField" };
        textField.RegisterValueChangedCallback(evt => newDialog.parameters = evt.newValue);
        dialogCase.Add(textField);

        dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, newDialog.uniqueID, viewNode)) { text = "X" });
        textContainer.Add(dialogCase);

        dialogueList = dialogueList.Append(newDialog).ToArray();
    }

    private void RemoveNodeContent(VisualElement container, string uniqueID, ViewDialogueNode viewNode)
    {
        container.RemoveFromHierarchy();
        dialogueList = dialogueList.Where(d => d.uniqueID != uniqueID).ToArray();
    }
}

