using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using System.Collections.Generic;
using UnityEditor.Experimental.GraphView;

public class NodeRandom : DialogueNodeSO, INodeFeature, IChildNodeSetter
{
    [System.Serializable]
    public class DialogueList
    {
        public string importance; // 权重
        public string percentage; // 概率
        public DialogueNodeSO childNode;

        [HideInInspector] public string uniqueID;
    }

    public DialogueList[] dialogueList = new DialogueList[0];
    private Dictionary<string, TextField> percentageFields = new Dictionary<string, TextField>();

    public NodeRandom()
    {
        title = "随机节点";

        dialogueList = Enumerable.Range(0, 2).Select(_ => new DialogueList
        {
            uniqueID = System.Guid.NewGuid().ToString(),
            importance = "1"
        }).ToArray(); // 添加两个默认元素
    }

    public void CreateNodeFeatures(VisualElement node)
    {
        var viewNode = node as ViewDialogueNode;
        if (viewNode == null)
        {
            Debug.LogError("Node is not of type ViewDialogueNode");
            return;
        }

        node.Q<VisualElement>("FunctionButtons") // 新建元素的按钮
        ?.Add(new Button(() => AddNodeContent(node.Q<VisualElement>("TextContent"), viewNode)) { text = "[ + ]" });

        var textContainer = node.Q<VisualElement>("TextContent");
        if (textContainer != null)
        {
            foreach (var dialogue in dialogueList)
            {
                CreateDialogueElement(textContainer, viewNode, dialogue);
            }
        }

        UpdatePercentages();
    }

    private void AddNodeContent(VisualElement textContainer, ViewDialogueNode viewNode)
    {
        var newDialog = new DialogueList { uniqueID = System.Guid.NewGuid().ToString(), importance = "1" };
        dialogueList = dialogueList.Append(newDialog).ToArray();

        CreateDialogueElement(textContainer, viewNode, newDialog);

        viewNode.OnRefreshNode();
        UpdatePercentages();
    }

    private void CreateDialogueElement(VisualElement textContainer, ViewDialogueNode viewNode, DialogueList dialogue)
    {
        var dialogCase = new VisualElement { name = "BranchingDialogue" };

        var icon1 = new Image { image = EditorGUIUtility.IconContent("IntegerField").image, name = "RandomIcon" };
        dialogCase.Add(icon1);

        var textField1 = new TextField { value = dialogue.importance, name = "RandomField" };
        textField1.RegisterValueChangedCallback(evt =>
        {
            dialogue.importance = evt.newValue;
            UpdatePercentages();
        });
        dialogCase.Add(textField1);

        var icon2 = new Image { image = EditorGUIUtility.IconContent("WaitSpin00").image, name = "RandomIcon" };
        dialogCase.Add(icon2);

        var textField2 = new TextField { value = dialogue.percentage, name = "RandomField", isReadOnly = true }; // 设置为只读
        dialogCase.Add(textField2);

        percentageFields[dialogue.uniqueID] = textField2; // 存储TextField

        dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, dialogue.uniqueID, viewNode)) { text = "X" });
        textContainer.Add(dialogCase);

        var output = viewNode.CreateOutputPort(true); // 创建输出端口
        if (output != null)
        {
            viewNode.outputContainer.Add(output);
            output.userData = dialogue.uniqueID;
        }
    }

    private void RemoveNodeContent(VisualElement container, string uniqueID, ViewDialogueNode viewNode)
    {
        var outputPort = viewNode.outputContainer.Children().FirstOrDefault(port => port.userData as string == uniqueID) as Port; // 找到对应的输出端口

        outputPort?.connections.ToList().ForEach(edge => // 删除连接线和输出端口
        {
            edge.input.Disconnect(edge);
            edge.RemoveFromHierarchy();
        });
        outputPort?.RemoveFromHierarchy();

        container.RemoveFromHierarchy(); // 删除容器及实例元素
        dialogueList = dialogueList.Where(d => d.uniqueID != uniqueID).ToArray();

        percentageFields.Remove(uniqueID); // 移除存储的TextField

        viewNode.OnRefreshNode();
        UpdatePercentages();
    }

    public void SetChildNode(string uniqueID, DialogueNodeSO childNode = null) // 连线对象
    {
        var dialogueListEntry = dialogueList.FirstOrDefault(d => d.uniqueID == uniqueID);
        if (dialogueListEntry != null)
        {
            dialogueListEntry.childNode = childNode;
        }
    }

    private void UpdatePercentages() // 计算元素概率
    {
        float totalImportance = dialogueList.Sum(d => float.TryParse(d.importance, out var value) ? value : 0);
        foreach (var dialogue in dialogueList)
        {
            if (float.TryParse(dialogue.importance, out var importanceValue) && totalImportance > 0)
            {
                dialogue.percentage = ((importanceValue / totalImportance) * 100).ToString("F2");
            }
            else
            {
                dialogue.percentage = "0";
            }

            if (percentageFields.TryGetValue(dialogue.uniqueID, out var textField2))
            {
                textField2.value = dialogue.percentage;
            }
        }
    }
}
