using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

public class NodeRoot : DialogueNodeSO, INodeFeature
{
    public DialogueNodeSO childNode;

    public NodeRoot()
    {
        title = "根节点";
    }

    public void CreateNodeFeatures(VisualElement node)
    {
        var textContainer = node.Q<VisualElement>("TextContent");
        var icon = new Image { image = EditorGUIUtility.IconContent("d_AnimatorStateTransition Icon").image, name = "RootIcon" };
        textContainer.Add(icon);
    }
}
