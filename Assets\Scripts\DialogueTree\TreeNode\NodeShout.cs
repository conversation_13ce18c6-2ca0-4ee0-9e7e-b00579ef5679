using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

public class NodeShout : DialogueNodeSO, INodeFeature
{
    public Shout direction; public enum Shout { Left, Right, Top, Bottom, TopLeft, BottomLeft, TopRight, BottomRight }

    [System.Serializable]
    public class DialogueList
    {
        public string textContent;
        public string textID;

        [HideInInspector] public string uniqueID;
    }

    public DialogueList[] dialogueList = new DialogueList[0];
    public DialogueNodeSO childNode;

    public NodeShout()
    {
        title = "呼喊节点";

        dialogueList = Enumerable.Range(0, 1).Select(_ => new DialogueList
        {
            uniqueID = System.Guid.NewGuid().ToString(),
        }).ToArray();
    }

    public void CreateNodeFeatures(VisualElement node)
    {
        var imgContainer = leftRight == LeftRight.Left
        ? node.Q<VisualElement>("LeftImage")
        : node.Q<VisualElement>("RightImage");

        if (imgContainer != null && roleImage != null)
        {
            imgContainer.Clear();
            var imgElement = new Image { image = roleImage.texture }; // 创建角色图片
            imgElement.AddToClassList("role-image");
            imgContainer.Add(imgElement);
        }

        var viewNode = node as ViewDialogueNode;
        if (viewNode == null)
        {
            Debug.LogError("Node is not of type ViewDialogueNode");
            return;
        }

        var middleView = node.Q<VisualElement>("TextContent");
        if (middleView != null)
        {
            var directionField = new EnumField { name = "directionField" };
            directionField.Init(direction);
            directionField.label = "";
            directionField.RegisterValueChangedCallback(evt => direction = (Shout)evt.newValue);
            middleView.Add(directionField);
        }

        node.Q<VisualElement>("FunctionButtons") // 新建元素的按钮
        ?.Add(new Button(() => AddNodeContent(node.Q<VisualElement>("TextContent"), viewNode)) { text = "[ + ]" });

        var textContainer = node.Q<VisualElement>("TextContent");
        if (textContainer != null)
        {
            foreach (var dialogue in dialogueList)
            {
                var dialogCase = new VisualElement { name = "BranchingDialogue" }; // 创建元素容器

                var textField = new TextField { value = dialogue.textContent, name = "textField" };
                textField.RegisterValueChangedCallback(evt => dialogue.textID = evt.newValue);

                dialogCase.Add(textField);
                dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, dialogue.uniqueID, viewNode)) { text = "X" });

                textContainer.Add(dialogCase);
            }
        }
    }

    private void AddNodeContent(VisualElement textContainer, ViewDialogueNode viewNode)
    {
        var newDialog = new DialogueList { uniqueID = System.Guid.NewGuid().ToString() };
        var dialogCase = new VisualElement { name = "BranchingDialogue" };

        var textField = new TextField { value = "", name = "textField" };
        textField.RegisterValueChangedCallback(evt => newDialog.textID = evt.newValue);
        dialogCase.Add(textField);

        dialogCase.Add(new Button(() => RemoveNodeContent(dialogCase, newDialog.uniqueID, viewNode)) { text = "X" });
        textContainer.Add(dialogCase);

        dialogueList = dialogueList.Append(newDialog).ToArray();
    }

    private void RemoveNodeContent(VisualElement container, string uniqueID, ViewDialogueNode viewNode)
    {
        container.RemoveFromHierarchy();
        dialogueList = dialogueList.Where(d => d.uniqueID != uniqueID).ToArray();
    }
}

