using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

public class ViewDialogueInspector : VisualElement
{
    private Editor editor; // 定义一个私有变量editor，用于存储编辑器实例
    private ScrollView scrollView; // 定义一个ScrollView变量

    public new class UxmlFactory : UxmlFactory<ViewDialogueInspector, VisualElement.UxmlTraits> { }

    public ViewDialogueInspector()
    {
        scrollView = new ScrollView(); // 初始化ScrollView
        scrollView.style.flexGrow = 1; // 设置ScrollView占据可用空间
        Add(scrollView); // 将ScrollView添加到VisualElement中
    }

    public void UpdateSelection(ViewDialogueNode view) // 定义一个方法，用于更新Inspector面板的显示内容
    {
        scrollView.Clear(); // 清空ScrollView中的内容

        if (view == null) // 如果选中的节点为空，直接返回
        {
            return;
        }

        if (view.node is NodeRoot nodeRoot) // 检查选中的节点是否为 NodeRoot 类型
        {
            Object.DestroyImmediate(editor); // 销毁当前的编辑器实例
            editor = Editor.CreateEditor(nodeRoot.tree); // 显示 DialogueTreeSO 资产的属性信息
            IMGUIContainer container = new IMGUIContainer(() => { editor.OnInspectorGUI(); });// 创建一个IMGUIContainer，用于在Inspector面板中显示编辑器GUI
            scrollView.Add(container); // 添加到ScrollView中
        }
        else
        {
            Object.DestroyImmediate(editor);
            editor = Editor.CreateEditor(view.node);
            IMGUIContainer container = new IMGUIContainer(() => { editor.OnInspectorGUI(); });
            scrollView.Add(container);
        }
    }
}
