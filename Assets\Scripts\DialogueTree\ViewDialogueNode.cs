using System;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.Experimental.GraphView;

public interface INodeFeature //定义节点特性接口
{
    void CreateNodeFeatures(VisualElement node);
}

public class ViewDialogueNode : UnityEditor.Experimental.GraphView.Node
{
    public Action<ViewDialogueNode> OnNodeSelected;
    public DialogueNodeSO node;

    public Port input;
    public Port output;

    public Port inputCond;
    public Port outputCond;

    public ViewDialogueNode(DialogueNodeSO node) : base("Assets/Editor/DialogueTree/DialogueViewerNode.uxml")
    {
        this.node = node;
        this.title = node.title;
        this.viewDataKey = node.guid;
        style.left = node.position.x;
        style.top = node.position.y;

        CreateInputPorts();
        CreateOutputPorts();
        CreateNodeFeatures();
        SetupClasses();

        OnRefreshNode();
    }

    private void CreateInputPorts() // 创建输入端口  Single单一端口，Multi多个端口。
    {
        if (node is NodeRoot) { }
        else if (node is NodeBranchingCond) { }
        else
        { input = InstantiatePort(Orientation.Vertical, Direction.Input, Port.Capacity.Single, typeof(bool)); }

        if (input != null)
        {
            input.portName = "";
            input.style.flexDirection = FlexDirection.Column;
            input.userData = node.guid;
            inputContainer.Add(input);
        }
    }

    private void CreateOutputPorts() // 创建输出端口
    {
        if (!(node is NodeEnd || node is NodeBranching || node is NodeRandom || node is NodeBranchingCond))
        {
            output = CreateOutputPort(true);
        }
    }

    public Port CreateOutputPort(bool addToContainer)
    {
        var output = InstantiatePort(Orientation.Vertical, Direction.Output, Port.Capacity.Single, typeof(bool));
        if (output != null)
        {
            output.portName = "";
            output.style.flexDirection = FlexDirection.ColumnReverse;
            output.userData = node.guid;
            if (addToContainer)
            {
                outputContainer.Add(output);
            }
        }
        return output;
    }

    private void CreateNodeFeatures() // 创建节点特点
    {
        if (node is INodeFeature nodeFeature)
        {
            nodeFeature.CreateNodeFeatures(this);
        }
    }

    private void SetupClasses() // 设置节点样式
    {
        string className = node switch
        {
            NodeRoot => "nodeRoot",
            NodeEnd => "nodeEnd",
            NodeBranching => "nodeBranching",
            NodeRandom => "nodeRandom",
            NodeCondition => "nodeCondition",
            NodeSequence => "nodeSequence",
            NodeBranchingCond => "nodeBranchingCond",
            NodeImplement => "nodeImplement",
            NodeShout => "nodeShout",
            _ => null
        };

        if (className != null)
        {
            AddToClassList(className);
        }
    }

    public void OnRefreshNode() // 刷新节点状态
    {
        RefreshExpandedState();
        RefreshPorts();
    }

    public override void OnSelected() // 节点选中
    {
        base.OnSelected();
        if (OnNodeSelected != null)
        {
            OnNodeSelected.Invoke(this);
        }
    }

    public override void OnUnselected() // 节点取消选中
    {
        base.OnUnselected();
        OnNodeSelected?.Invoke(null);
    }

    public override void SetPosition(Rect newPos)
    {
        base.SetPosition(newPos);
        node.position.x = newPos.xMin;
        node.position.y = newPos.yMin;
    }
}
