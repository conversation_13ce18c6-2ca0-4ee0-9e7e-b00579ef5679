using System;
using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using System.Collections.Generic;
using UnityEditor.Experimental.GraphView;

public interface IChildNodeSetter // 节点子节点设置接口
{
    void SetChildNode(string uniqueID, DialogueNodeSO childNode = null);
}

public class ViewDialogueTree : GraphView
{
    public new class UxmlFactory : UxmlFactory<ViewDialogueTree, GraphView.UxmlTraits> { }

    public DialogueTreeSO tree; // 节点树视图
    public Action<ViewDialogueNode> OnNodeSelected;

    public ViewDialogueTree()
    {
        Insert(0, new GridBackground()); // 背景
        this.AddManipulator(new ContentZoomer()); // 视图缩放
        this.AddManipulator(new ContentDragger()); // 视图拖动
        this.AddManipulator(new SelectionDragger()); // 节点选中拖动
        this.AddManipulator(new RectangleSelector()); // 框形选中

        var styleSheet = AssetDatabase.LoadAssetAtPath<StyleSheet>("Assets/Editor/DialogueTree/DialogueViewer.uss"); // 加载节点树样式表
        styleSheets.Add(styleSheet); // 添加样式表
    }

    public override void BuildContextualMenu(ContextualMenuPopulateEvent evt) // 右键菜单(覆盖原版)
    {
        var mousePosition = evt.mousePosition; // 获取鼠标点击位置（屏幕坐标）
        var localMousePosition = contentViewContainer.WorldToLocal(mousePosition); // 转换为本地坐标

        var types = TypeCache.GetTypesDerivedFrom<DialogueNodeSO>(); // 获取所有派生自DialogueNodeSO的类型
        foreach (var nodeType in types)
        {
            if (nodeType != typeof(NodeRoot)) // 屏蔽 NodeRoot 节点的创建选项
            {
                evt.menu.AppendAction($"{nodeType.Name}", (a) => CreateNode(nodeType, localMousePosition));
            }
        }
    }

    public void EnsureRootNodeExists(DialogueTreeSO tree)
    {
        this.tree = tree;

        // 检查是否存在 NodeRoot 节点
        if (!tree.allNodes.OfType<NodeRoot>().Any())
        {
            Vector2 rootPosition = new Vector2(300, 100); // 设置 NodeRoot 节点的位置
            NodeRoot rootNode = tree.CreateNode(typeof(NodeRoot), rootPosition) as NodeRoot;
            rootNode.title = tree.name; // 将标题设置为 DialogueTreeSO 资产的名称

            // 创建节点视图并添加到视图中
            CreateNodeView(rootNode);

            // 选中 NodeRoot 节点并更新 ViewDialogueInspector
            OnNodeSelected?.Invoke(FindNodeView(rootNode));
        }
    }

    private void CreateNode(Type nodeType, Vector2 position) // 创建节点
    {
        DialogueNodeSO node = tree.CreateNode(nodeType, position); // 创建节点数据
        node.position = position; // 设置节点位置
        CreateNodeView(node);
    }
    private void CreateNodeView(DialogueNodeSO node) // 创建节点
    {
        ViewDialogueNode nodeView = new ViewDialogueNode(node); // 创建节点视图
        nodeView.OnNodeSelected = OnNodeSelected; // 节点选中事件
        AddElement(nodeView); // 添加节点到视图

        // 绑定端口的唯一标识符
        if (nodeView.input != null)
        {
            nodeView.input.userData = node.guid;
        }
        if (nodeView.output != null)
        {
            nodeView.output.userData = node.guid;
        }
    }

    public override void ClearSelection() // 清除节点选中
    {
        base.ClearSelection();
        OnNodeSelected?.Invoke(null);
    }

    private GraphViewChange OnGraphViewChanged(GraphViewChange graphViewChange)
    {
        if (graphViewChange.edgesToCreate != null)
        {
            foreach (var edge in graphViewChange.edgesToCreate)
            {
                var parentView = edge.output.node as ViewDialogueNode;
                var childView = edge.input.node as ViewDialogueNode;

                if (parentView.node is NodeCondition nodeCondition && edge.output.name == "CondOut")
                {
                    nodeCondition.notCompliant = childView.node as DialogueNodeSO;
                    continue;
                }

                tree.AddChild(parentView.node, childView.node);
                SetChildNode(parentView.node, (string)edge.output.userData, childView.node);

                if (edge.output.name == "ConditionPort" && edge.input.name == "BranchingPort")
                {
                    if (childView.node is NodeBranching nodeBranching && parentView.node is NodeBranchingCond)
                    {
                        nodeBranching.SetConditionPort((string)edge.input.userData, parentView.node);
                    }
                }
            }
        }

        if (graphViewChange.elementsToRemove != null)
        {
            foreach (var elem in graphViewChange.elementsToRemove)
            {
                if (elem is ViewDialogueNode nodeView)
                {
                    tree.DeleteNode(nodeView.node);
                    RemoveRelatedEdges(nodeView);
                }
                else if (elem is Edge edge)
                {
                    var parentView = edge.output.node as ViewDialogueNode;
                    var childView = edge.input.node as ViewDialogueNode;
                    tree.RemoveChild(parentView.node, childView.node);

                    SetChildNode(parentView.node, (string)edge.output.userData);

                    if (parentView.node is NodeCondition nodeCondition && edge.output.name == "CondOut")
                    {
                        nodeCondition.notCompliant = null;
                    }

                    if (edge.output.name == "ConditionPort" && edge.input.name == "BranchingPort")
                    {
                        if (childView.node is NodeBranching nodeBranching && parentView.node is NodeBranchingCond)
                        {
                            nodeBranching.SetConditionPort((string)edge.input.userData, null);
                        }
                    }
                }
            }
        }

        return graphViewChange;
    }

    private void RemoveRelatedEdges(ViewDialogueNode nodeView) // 删除节点相关的连接线
    {
        var edgesToRemove = edges.Where(edge =>
            (edge.output.node == nodeView && edge.output.name == "ConditionPort" && edge.input.name == "BranchingPort") ||
            (edge.input.node == nodeView && edge.output.name == "ConditionPort" && edge.input.name == "BranchingPort") ||
            (edge.output.node == nodeView && edge.output.name == "CondOut") ||
            (edge.input.node == nodeView && edge.input.name == "CondOut")).ToList();

        foreach (var edge in edgesToRemove)
        {
            edge.output.Disconnect(edge);
            edge.input.Disconnect(edge);
            edge.RemoveFromHierarchy();
        }

        if (nodeView.node is NodeBranchingCond)
        {
            foreach (var n in nodes.OfType<ViewDialogueNode>().Where(n => n.node is NodeBranching))
            {
                var nodeBranching = n.node as NodeBranching;
                var dialogue = nodeBranching.dialogueList.FirstOrDefault(d => d.conditionPort == nodeView.node);
                if (dialogue != null)
                {
                    dialogue.conditionPort = null;
                }
            }
        }
    }

    private void SetChildNode(DialogueNodeSO parentNode, string uniqueID, DialogueNodeSO childNode = null)
    {
        if (parentNode is IChildNodeSetter childNodeSetter)
        {
            childNodeSetter.SetChildNode(uniqueID, childNode); // 绑定端口与节点对象
        }
    }

    public void PopulateView(DialogueTreeSO tree) // 刷新视图
    {
        this.tree = tree;
        graphViewChanged -= OnGraphViewChanged;
        DeleteElements(graphElements);
        graphViewChanged += OnGraphViewChanged;

        // 创建节点视图
        tree.allNodes.ForEach(n => CreateNodeView(n));

        // 创建连接线
        tree.allNodes.ForEach(n =>
        {
            if (n is IChildNodeSetter childNodeSetter)
            {
                // 获取 dialogueList 字段的类型
                var dialogueListField = childNodeSetter.GetType().GetField("dialogueList");
                if (dialogueListField != null)
                {
                    // 获取 dialogueList 的值并转换为 IEnumerable
                    var dialogueList = dialogueListField.GetValue(childNodeSetter) as IEnumerable<object>;
                    if (dialogueList != null)
                    {
                        foreach (var dialogue in dialogueList)
                        {
                            // 使用反射获取 childNode 和 uniqueID 属性
                            var childNodeProperty = dialogue.GetType().GetField("childNode");
                            var uniqueIDProperty = dialogue.GetType().GetField("uniqueID");

                            if (childNodeProperty != null && uniqueIDProperty != null)
                            {
                                var childNode = childNodeProperty.GetValue(dialogue) as DialogueNodeSO;
                                var uniqueID = uniqueIDProperty.GetValue(dialogue) as string;

                                if (childNode != null)
                                {
                                    ViewDialogueNode parentView = FindNodeView(n);
                                    ViewDialogueNode childView = FindNodeView(childNode);
                                    if (parentView != null && childView != null)
                                    {
                                        var outputPort = parentView.Query<Port>().Where(port => port.userData as string == uniqueID && port.direction == Direction.Output).First();
                                        var inputPort = childView.Query<Port>().Where(port => port.direction == Direction.Input).First();
                                        if (outputPort != null && inputPort != null)
                                        {
                                            var edge = outputPort.ConnectTo(inputPort);
                                            AddElement(edge);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            else
            {
                var parentView = FindNodeView(n);
                var parentNodes = tree.GetChildren(n);
                parentNodes.ForEach(c =>
                {
                    var childView = FindNodeView(c);
                    var outputPort = parentView.Query<Port>().Where(port => port.userData as string == n.guid && port.direction == Direction.Output).First();
                    var inputPort = childView.Query<Port>().Where(port => port.userData as string == c.guid && port.direction == Direction.Input).First();
                    if (outputPort != null && inputPort != null)
                    {
                        AddElement(outputPort.ConnectTo(inputPort));
                    }
                });
            }

            // 恢复 NodeBranching 和 NodeBranchingCond 之间的连接线
            if (n is NodeBranching nodeBranching)
            {
                foreach (var dialogue in nodeBranching.dialogueList)
                {
                    if (dialogue.conditionPort != null)
                    {
                        ViewDialogueNode parentView = FindNodeView(dialogue.conditionPort);
                        ViewDialogueNode childView = FindNodeView(n);
                        if (parentView != null && childView != null)
                        {
                            // 从自定义容器中查找端口
                            var inputPort = childView.Query<Port>().Where(port => port.userData as string == dialogue.uniqueID && port.name == "BranchingPort" && port.direction == Direction.Input).First();
                            var outputPort = parentView.Query<Port>().Where(port => port.name == "ConditionPort" && port.direction == Direction.Output).First();

                            if (inputPort != null && outputPort != null)
                            {
                                var edge = outputPort.ConnectTo(inputPort);
                                AddElement(edge);
                            }
                        }
                    }
                }
            }

            // 恢复 NodeCondition 的 CondOut 端口连接
            if (n is NodeCondition nodeCondition && nodeCondition.notCompliant != null)
            {
                ViewDialogueNode parentView = FindNodeView(n);
                ViewDialogueNode childView = FindNodeView(nodeCondition.notCompliant);
                if (parentView != null && childView != null)
                {
                    var outputPort = parentView.Query<Port>().Where(port => port.name == "CondOut" && port.direction == Direction.Output).First();
                    var inputPort = childView.Query<Port>().Where(port => port.direction == Direction.Input).First();
                    if (outputPort != null && inputPort != null)
                    {
                        var edge = outputPort.ConnectTo(inputPort);
                        AddElement(edge);
                    }
                }
            }
        });
    }

    public ViewDialogueNode FindNodeView(DialogueNodeSO node)
    {
        return GetNodeByGuid(node.guid) as ViewDialogueNode; // 通过 GUID 查找节点视图
    }

    public override List<Port> GetCompatiblePorts(Port startPort, NodeAdapter nodeAdapter) // 节点端口连接规则
    {
        return ports.Where(endPort => endPort.direction != startPort.direction && endPort.node != startPort.node).ToList();
    }
}