using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using Newtonsoft.Json.Linq;

// 语言管理器 - 负责语言数据的加载、缓存和文本获取
public class LanguageManager : SingletonMono<LanguageManager>
{
    #region 私有字段

    // 语言数据缓存字典 - 存储所有文本ID对应的多语言内容
    private static Dictionary<string, List<string>> _languageCache = new Dictionary<string, List<string>>();

    /// <summary>
    /// 当前加载的语言
    /// </summary>
    private static string _currentLanguage = string.Empty;

    private static bool _isInitialized = false; // 缓存是否已初始化

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取当前语言
    /// </summary>
    public string CurrentLanguage => _currentLanguage;

    /// <summary>
    /// 检查是否有缓存数据
    /// </summary>
    public bool HasCachedData => _languageCache.Count > 0 && _isInitialized;

    #endregion

    #region 生命周期

    void Awake()
    {
        // 确保单例在场景切换时不被销毁
        if (Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        DontDestroyOnLoad(gameObject);
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 初始化语言管理器 - 从配置中获取语言设置并加载数据
    /// </summary>
    /// <param name="configData">配置数据</param>
    public void Initialize(JObject configData)
    {
        if (configData == null)
        {
            Debug.LogError("LanguageManager: 配置数据为空");
            return;
        }

        string language = UpdateLanguageFromConfig(configData);
        LoadLanguageData(language);
    }

    /// <summary>
    /// 从配置更新语言设置
    /// </summary>
    /// <param name="configData">配置数据</param>
    /// <returns>确定的语言设置</returns>
    public string UpdateLanguageFromConfig(JObject configData)
    {
        string language = (string)configData["Language"];

        if (string.IsNullOrEmpty(language))
        {
            // 根据系统语言设置默认语言
            if (Application.systemLanguage == SystemLanguage.Chinese ||
                Application.systemLanguage == SystemLanguage.ChineseSimplified ||
                Application.systemLanguage == SystemLanguage.ChineseTraditional)
            {
                language = "简体中文";
            }
            else
            {
                language = "English";
            }

            // 更新配置数据
            configData["Language"] = language;
            Debug.Log($"LanguageManager: 设置默认语言为 '{language}'");
        }

        return language;
    }

    /// <summary>
    /// 获取指定ID的文本内容
    /// </summary>
    /// <param name="textID">文本ID</param>
    /// <returns>对应的文本内容，如果找不到则返回空字符串</returns>
    public string GetText(string textID)
    {
        if (string.IsNullOrEmpty(textID))
        {
            Debug.LogWarning("LanguageManager: 文本ID为空");
            return string.Empty;
        }

        if (_languageCache.TryGetValue(textID, out var values) && values.Count > 1)
        {
            return values[1]; // 返回第二列的内容（索引1）
        }

        Debug.LogError($"LanguageManager: 无法找到ID '{textID}' 的文本内容");
        return string.Empty;
    }

    /// <summary>
    /// 加载指定语言的数据
    /// </summary>
    /// <param name="language">语言名称</param>
    /// <param name="forceReload">是否强制重新加载</param>
    public void LoadLanguageData(string language, bool forceReload = false)
    {
        if (string.IsNullOrEmpty(language))
        {
            Debug.LogError("LanguageManager: 语言参数为空");
            return;
        }

        // 检查是否需要重新加载
        if (!forceReload && _isInitialized && _currentLanguage == language && _languageCache.Count > 0)
        {
            Debug.Log($"LanguageManager: 语言 '{language}' 已缓存，跳过加载");
            return;
        }

        string csvPath = Path.Combine(Application.streamingAssetsPath, "language", language + ".csv");

        if (!File.Exists(csvPath))
        {
            Debug.LogError($"LanguageManager: 语言文件不存在: {csvPath}");
            return;
        }

        try
        {
            // 清空旧缓存
            _languageCache.Clear();

            // 读取CSV文件
            string[] lines = File.ReadAllLines(csvPath);

            foreach (string line in lines)
            {
                if (string.IsNullOrEmpty(line)) continue;

                string[] parts = line.Split(',');
                if (parts.Length >= 3) // 确保至少有3列数据
                {
                    string textID = parts[1].Trim();
                    if (!string.IsNullOrEmpty(textID))
                    {
                        _languageCache[textID] = parts.Skip(2).ToList(); // 从第3列开始存储
                    }
                }
            }

            _currentLanguage = language;
            _isInitialized = true;

            Debug.Log($"LanguageManager: 成功加载语言 '{language}'，共 {_languageCache.Count} 条文本");

            // 发送语言更新事件
            EventCenter.Instance?.SendEvent("OnLanguageChanged");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"LanguageManager: 加载语言文件时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查指定语言是否已缓存
    /// </summary>
    /// <param name="language">语言名称</param>
    /// <returns>是否已缓存</returns>
    public bool HasCachedLanguage(string language)
    {
        return _isInitialized && _currentLanguage == language && _languageCache.Count > 0;
    }

    /// <summary>
    /// 清除语言缓存
    /// </summary>
    public void ClearCache()
    {
        _languageCache.Clear();
        _currentLanguage = string.Empty;
        _isInitialized = false;
        Debug.Log("LanguageManager: 缓存已清除");
    }

    /// <summary>
    /// 获取缓存的文本数量
    /// </summary>
    /// <returns>缓存的文本条数</returns>
    public int GetCachedTextCount()
    {
        return _languageCache.Count;
    }

    #endregion

    #region 调试方法

    /// <summary>
    /// 打印缓存状态信息（调试用）
    /// </summary>
    [System.Diagnostics.Conditional("UNITY_EDITOR")]
    public void PrintCacheStatus()
    {
        Debug.Log($"LanguageManager 缓存状态:");
        Debug.Log($"- 当前语言: {_currentLanguage}");
        Debug.Log($"- 已初始化: {_isInitialized}");
        Debug.Log($"- 缓存文本数量: {_languageCache.Count}");
    }

    #endregion
}
