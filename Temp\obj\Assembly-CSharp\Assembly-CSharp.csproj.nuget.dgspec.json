{"format": 1, "restore": {"F:\\The Lightless Crown\\Assembly-CSharp.csproj": {}}, "projects": {"F:\\The Lightless Crown\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\The Lightless Crown\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "F:\\The Lightless Crown\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\The Lightless Crown\\Temp\\obj\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj": {"projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj"}, "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj": {"projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj"}, "F:\\The Lightless Crown\\VTabs.csproj": {"projectPath": "F:\\The Lightless Crown\\VTabs.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "F:\\The Lightless Crown\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\The Lightless Crown\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "F:\\The Lightless Crown\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\The Lightless Crown\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\The Lightless Crown\\Assembly-CSharp-firstpass.csproj": {"projectPath": "F:\\The Lightless Crown\\Assembly-CSharp-firstpass.csproj"}, "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj": {"projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj"}, "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj": {"projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj"}, "F:\\The Lightless Crown\\VTabs.csproj": {"projectPath": "F:\\The Lightless Crown\\VTabs.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj", "projectName": "UltimateEditorEnhancer-Editor", "projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\The Lightless Crown\\Temp\\obj\\UltimateEditorEnhancer-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj": {"projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj", "projectName": "UltimateEditorEnhancer", "projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\The Lightless Crown\\Temp\\obj\\UltimateEditorEnhancer\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "F:\\The Lightless Crown\\VTabs.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\The Lightless Crown\\VTabs.csproj", "projectName": "VTabs", "projectPath": "F:\\The Lightless Crown\\VTabs.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\The Lightless Crown\\Temp\\obj\\VTabs\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}}