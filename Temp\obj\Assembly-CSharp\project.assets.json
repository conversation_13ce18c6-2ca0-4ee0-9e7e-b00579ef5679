{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UltimateEditorEnhancer": "1.0.0", "UltimateEditorEnhancer-Editor": "1.0.0", "VTabs": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}}, "UltimateEditorEnhancer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UltimateEditorEnhancer.dll": {}}, "runtime": {"bin/placeholder/UltimateEditorEnhancer.dll": {}}}, "UltimateEditorEnhancer-Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UltimateEditorEnhancer": "1.0.0"}, "compile": {"bin/placeholder/UltimateEditorEnhancer-Editor.dll": {}}, "runtime": {"bin/placeholder/UltimateEditorEnhancer-Editor.dll": {}}}, "VTabs/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/VTabs.dll": {}}, "runtime": {"bin/placeholder/VTabs.dll": {}}}}}, "libraries": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "path": "Assembly-CSharp-firstpass.csproj", "msbuildProject": "Assembly-CSharp-firstpass.csproj"}, "UltimateEditorEnhancer/1.0.0": {"type": "project", "path": "UltimateEditorEnhancer.csproj", "msbuildProject": "UltimateEditorEnhancer.csproj"}, "UltimateEditorEnhancer-Editor/1.0.0": {"type": "project", "path": "UltimateEditorEnhancer-Editor.csproj", "msbuildProject": "UltimateEditorEnhancer-Editor.csproj"}, "VTabs/1.0.0": {"type": "project", "path": "VTabs.csproj", "msbuildProject": "VTabs.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp-firstpass >= 1.0.0", "UltimateEditorEnhancer >= 1.0.0", "UltimateEditorEnhancer-Editor >= 1.0.0", "VTabs >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\The Lightless Crown\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "F:\\The Lightless Crown\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\The Lightless Crown\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\The Lightless Crown\\Assembly-CSharp-firstpass.csproj": {"projectPath": "F:\\The Lightless Crown\\Assembly-CSharp-firstpass.csproj"}, "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj": {"projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj"}, "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj": {"projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj"}, "F:\\The Lightless Crown\\VTabs.csproj": {"projectPath": "F:\\The Lightless Crown\\VTabs.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}