{"version": 3, "targets": {".NETStandard,Version=v2.1": {"UltimateEditorEnhancer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UltimateEditorEnhancer.dll": {}}, "runtime": {"bin/placeholder/UltimateEditorEnhancer.dll": {}}}}}, "libraries": {"UltimateEditorEnhancer/1.0.0": {"type": "project", "path": "UltimateEditorEnhancer.csproj", "msbuildProject": "UltimateEditorEnhancer.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["UltimateEditorEnhancer >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj", "projectName": "UltimateEditorEnhancer-Editor", "projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\The Lightless Crown\\Temp\\obj\\UltimateEditorEnhancer-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj": {"projectPath": "F:\\The Lightless Crown\\UltimateEditorEnhancer.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}